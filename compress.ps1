# 设置输出编码为UTF-8解决中文乱码
chcp 65001 | Out-Null
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# 设置UPX路径
$upxPath = "D:/upx/upx.exe"

# 检查UPX是否存在
if (-not (Test-Path $upxPath)) {
    Write-Host "错误：UPX工具未找到，请确认路径：$upxPath" -ForegroundColor Red
    exit 1
}

# 检查二进制文件是否存在
$binaryPath = "output/gpugovernor"
if (-not (Test-Path $binaryPath)) {
    Write-Host "错误：二进制文件未找到，请确认路径：$binaryPath" -ForegroundColor Red
    exit 1
}

# 显示压缩前的文件大小
$originalSize = (Get-Item $binaryPath).Length
Write-Host "压缩前文件大小: $($originalSize) 字节" -ForegroundColor Cyan

# 使用UPX进行LZMA压缩
Write-Host "正在使用UPX进行LZMA压缩..." -ForegroundColor Cyan
& $upxPath --lzma $binaryPath

# 显示压缩后的文件大小
$compressedSize = (Get-Item $binaryPath).Length
$ratio = [math]::Round(($compressedSize / $originalSize) * 100, 2)
Write-Host "压缩后文件大小: $($compressedSize) 字节 ($ratio% 的原始大小)" -ForegroundColor Green

# 复制压缩后的文件到一个新的位置
$compressedPath = "output/gpugovernor_compressed"
Copy-Item $binaryPath $compressedPath
Write-Host "压缩后的文件已复制到: $compressedPath" -ForegroundColor Green
