# Set environment variables
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$env:ANDROID_NDK_HOME = "D:/android-ndk-r27c"
$env:LLVM_PATH = "D:/LLVM"
$env:PATH = "$env:LLVM_PATH/bin;$env:ANDROID_NDK_HOME/toolchains/llvm/prebuilt/windows-x86_64/bin;$env:PATH"
$env:CARGO_TARGET_AARCH64_LINUX_ANDROID_LINKER = "$env:ANDROID_NDK_HOME/toolchains/llvm/prebuilt/windows-x86_64/bin/aarch64-linux-android33-clang.cmd"
$env:LIBCLANG_PATH = "$env:LLVM_PATH/bin"
$env:BINDGEN_EXTRA_CLANG_ARGS = "--target=aarch64-linux-android -I$env:ANDROID_NDK_HOME/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include"

# Build
cargo build --release --target aarch64-linux-android

# Create output directory and copy binary
if (-not (Test-Path "output")) {
    New-Item -ItemType Directory -Path "output"
}

if (Test-Path "target/aarch64-linux-android/release/gpugovernor") {
    Copy-Item "target/aarch64-linux-android/release/gpugovernor" "output/"
    Write-Host "Build successful, binary copied to output/ directory"
} else {
    Write-Host "Build failed, output file not found"
}
